<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getUserInfo, logout } from '@/request/loginApi'
import { ElMessageBox, ElMessage } from 'element-plus'

const router = useRouter()
const user = ref({})

onMounted(async () => {
  try {
    const res = await getUserInfo()
    user.value = res.user || res.data || res
  } catch (e) {
    ElMessage.error('获取用户信息失败')
  }
})

// 退出登录逻辑
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    try {
      await logout()
      localStorage.removeItem('token')
      ElMessage.success('退出登录成功')
      router.push('/login')
    } catch (error) {
      console.error('退出登录失败:', error)
      localStorage.removeItem('token')
      router.push('/login')
    }
  } catch {
    // 用户取消操作
  }
}
</script>

<template>
  <div class="profile-container">
    <el-card shadow="hover" class="profile-card">
      <template #header>
        <div class="header-title">
          <el-icon><User /></el-icon>
          <span>当前用户个人信息</span>
        </div>
      </template>

      <el-descriptions column="2" border>
        <el-descriptions-item label="用户名">{{ user.userName }}</el-descriptions-item>
        <el-descriptions-item label="昵称">{{ user.nickName }}</el-descriptions-item>
        <el-descriptions-item label="邮箱">{{ user.email }}</el-descriptions-item>
        <el-descriptions-item label="手机">{{ user.phonenumber }}</el-descriptions-item>
        <el-descriptions-item label="性别">
          {{ user.sex === '1' ? '女' : user.sex === '0' ? '男' : '未知' }}
        </el-descriptions-item>
        <!-- <el-descriptions-item label="所属部门">
          {{ user.dept?.deptName || '—' }}
        </el-descriptions-item> -->
        <el-descriptions-item label="角色">
          <el-tag type="success" v-if="user.roles?.length">{{ user.roles[0].roleName }}</el-tag>
        </el-descriptions-item>
        <!-- <el-descriptions-item label="登录 IP">{{ user.loginIp }}</el-descriptions-item> -->
        <el-descriptions-item label="登录时间">
          {{ user.loginDate ? user.loginDate.slice(0,19).replace('T',' ') : '—' }}
        </el-descriptions-item>
        <el-descriptions-item label="">
                <div class="setting-item">
          <el-button type="danger" @click="handleLogout">退出登录</el-button>
        </div>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
  </div>
</template>

<style scoped>
.profile-container {
  padding: 20px;
}
.profile-card {
  max-width: 800px;
  margin: 0 auto;
}
.header-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
  gap: 6px;
}
</style>
