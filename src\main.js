import './assets/main.css'

import { createApp } from 'vue'
import App from './App.vue'
import router from './router'; // 引入路由配置文件
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import zhCn from 'element-plus/es/locale/lang/zh-cn' // 引入中文语言包
import '@/utils/flexible.js'

const app = createApp(App)
app.use(ElementPlus, { locale: zhCn }) // 使用中文语言包
app.use(router); // 使用路由配置
app.mount('#app')

