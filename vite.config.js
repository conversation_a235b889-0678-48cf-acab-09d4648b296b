import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import postcssPxtoRem from "postcss-pxtorem";
import VueRouter from 'unplugin-vue-router/vite'
// import vueDevTools from 'vite-plugin-vue-devtools'
// 后端接口
const baseUrl = 'http://ai.b7cool.com:8282'
// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    // 自动路由
    VueRouter({routesFolder: ['src/views'],}),
    AutoImport({
      imports: ['vue', 'vue-router']
    }),
    Components({
      resolvers: [ElementPlusResolver()],
    }),
    // vueDevTools(),
  ],
  server: {
    host: '0.0.0.0',
    proxy: {
      // 代理路径
      '/dev-api': {
        target: baseUrl, // 目标服务器地址
        changeOrigin: true, // 是否修改请求头中的 Origin 字段
        rewrite: (path) => path.replace(/^\/dev-api/, ''), // 重写路径
      },
    },
  },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
  build: {
    target: 'esnext'
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@use "@/common/variables.scss" as *;`
      }
    },
    postcss: {
      plugins: [
        postcssPxtoRem({
          rootValue: 192, // 按照自己的设计稿修改 1920/10
          unitPrecision: 5, // 保留到5位小数
          selectorBlackList: ["ignore"], // 忽略转换正则匹配项
          propList: ["*", "!border"],
          replace: true,
          mediaQuery: false, // 媒体查询( @media screen 之类的)中不生效
          minPixelValue: 1, //可以选择px小于1的不会被转换
        }),
      ],
    },
  }
})
