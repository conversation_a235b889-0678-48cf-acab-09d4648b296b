<script setup>
import { ref, reactive,onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { login, getCodeImg} from '@/request/loginApi'


const router = useRouter()
const loginFormRef = ref(null)

const loginForm = reactive({
    username: 'admin',//admin
    password: 'admin123',//admin123  
    code: '' ,// 验证码字段
    uuid: '',
    rememberMe: false
})

const loading = ref(false)

const loginRules = {
    username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
    password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
    code: [{ required: true, message: '请输入验证码', trigger: 'blur' }] // 验证码校验规则
}

// 验证码图片
const captchaImg = ref('');
//点击图片更新验证码信息，也会更新uuid
const getCaptcha = async () => {
  const res = await getCodeImg()
  captchaImg.value = 'data:image/gif;base64,' + res.img
  loginForm.uuid = res.uuid
}

const handleLogin = async () => {
    if (!loginFormRef.value) return

    await loginFormRef.value.validate(async (valid) => {
        if (valid) {
            loading.value = true
            try {
                // 简易账号密码验证（开发测试用）
                // if (loginForm.username === '1' && loginForm.password === '1' && loginForm.captcha === '1') {
                //     // 模拟成功登录
                //     localStorage.setItem('token', 'dev-token-1')
                //     ElMessage.success('登录成功')
                //     router.push('/')
                //     loading.value = false
                //     return
                // }

                // 正式登录逻辑（可在上线时取消注释）
                const res = await login(
                    loginForm.username,
                    loginForm.password,
                    loginForm.code,
                    loginForm.uuid
                );
                console.log('登录响应：', res);
                if (res.code === 200) {
                    const token = res.token || res.data?.token
                    console.log('登录响应完整内容：', res);
                    console.log('登录存储 token：',token);
                    localStorage.setItem('token',token)
                    console.log('✅ 登录成功，准备跳转');
                    ElMessage.success('登录成功')
                    router.push('/')
                } else {
                    // 登录失败，刷新验证码
                    getCaptcha()
                    // console.log('❌ 登录失败，原因：', res.msg)
                    ElMessage.error(res.msg || '登录失败')
                }
            } catch (error) {
                console.error('登录错误:', error)
                getCaptcha()
                ElMessage.error('登录失败，请稍后重试')
            } finally {
                loading.value = false
            }
        }
    })
}

// 组件挂载时初始化验证码
onMounted( async () => {
    let res = await getCodeImg()
    captchaImg.value = 'data:image/gif;base64,' + res.img;
    loginForm.uuid = res.uuid;
})
</script>

<template>
    <div class="login-container">
        <div class="login-box">
            <div class="title">AI风控智能评估平台</div>
            <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" class="login-form">
                <el-form-item prop="username">
                    <el-input
                     v-model="loginForm.username" 
                     placeholder="用户名" 
                     prefix-icon="User" />
                </el-form-item>
                <el-form-item prop="password">
                    <el-input 
                    v-model="loginForm.password" 
                    type="password" 
                    placeholder="密码" 
                    prefix-icon="Lock"
                    show-password
                     @keyup.enter="handleLogin" />
                </el-form-item>
                <el-form-item prop="code">
                    <div class="captcha-container">
                        <el-input 
                            v-model="loginForm.code" 
                            placeholder="验证码" 
                            prefix-icon="VerificationCode"
                            style="width: 50%; 
                            margin-right: 10%;
                            " @keyup.enter="handleLogin" />
                        <img
                            :src="captchaImg" 
                            @click="getCaptcha" 
                            alt="验证码" 
                            class="captcha-image"
                            title="点击刷新验证码"/>
                    </div>
                </el-form-item>
                <el-form-item>
                    <el-checkbox v-model="loginForm.rememberMe" style="margin-bottom: 15px;">记住我</el-checkbox>
                </el-form-item>
                <el-form-item>
                    <el-button :loading="loading" 
                    type="primary" 
                    class="login-button"
                     @click="handleLogin">
                     <span v-if="!loading">登 录</span>
                     <span v-else>登 录 中...</span>
                    </el-button>
                </el-form-item>
            </el-form>
        </div>
    </div>
</template>

<style scoped>
.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    width: 100vw;
    position: fixed;
    top: 0;
    left: 0;
    background-color: #000000;
    overflow: hidden;
}

.login-box {
    width: 400px;
    padding: 40px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.title {
    font-size: 24px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 30px;
    color: #001B48;
}

.login-form {
    width: 100%;
}

.login-button {
    width: 100%;
    background-color: #001B48;
    height: 40px;
    font-size: 16px;
    transition: all 0.3s;
}

.login-button:hover {
    opacity: 0.9;
    transform: translateY(-2px);
}

.captcha-container {
    display: flex;
    align-items: center;
}

.captcha-image {
    height: 40px;
    width: 120px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
}

.captcha-image:hover {
    transform: scale(1.02);
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.15);
}

/* 响应式调整 */
@media screen and (max-width: 480px) {
    .login-box {
        width: 90%;
        padding: 30px;
    }
}
</style>