// src/router/index.js
import { createRouter, createWebHistory } from 'vue-router';
import {routes} from 'vue-router/auto-routes'

import RiskEvaluate from '@/views/hotelReview/riskEvaluate.vue';

import Login from '@/views/login/index.vue';
const routes1 = [
    {
        path:'/',
        redirect: '/riskEvaluate'
    },
    {
        path: '/riskEvaluate',
        name: 'riskEvaluate',
        component: RiskEvaluate,
    },
    {
        path: '/login',
        name: 'login',
        component: Login,
        meta: { requiresAuth: false }
    },
    {
        path: '/profile',
        name: 'profile',
        component: () => import('@/views/profile/index.vue'),
        meta: { requiresAuth: true }
      },
      {
        path: '/user',
        name: 'user',
        component: () => import('@/views/user/index.vue'),
        meta: { requiresAuth: true }
      },
      {
        path: '/listModel',
        name: 'listModel',
        component: () => import('@/views/listModel/index.vue'),
        meta: { requiresAuth: true }
      }      
];

const router = createRouter({
    history: createWebHistory(""),
    routes: [...routes,...routes1],
});

// 添加路由守卫
router.beforeEach((to, from, next) => {
    const token = localStorage.getItem('token')
    
    if (to.meta.requiresAuth !== false && !token) {
        // 需要登录但没有token，重定向到登录页
        next({ name: 'login' })
    } else {
        next()
    }
})

export default router;