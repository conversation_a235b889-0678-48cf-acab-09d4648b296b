<script setup>
import { useRouter } from 'vue-router'
import { ElMessageBox, ElMessage } from 'element-plus'
import { logout } from '@/request/loginApi'

const router = useRouter()

const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    try {
      // 调用退出登录接口
      await logout()
      // 清除本地存储的token
      localStorage.removeItem('token')
      ElMessage.success('退出登录成功')
      // 跳转到登录页
      router.push('/login')
    } catch (error) {
      console.error('退出登录失败:', error)
      // 即使接口调用失败，也清除token并跳转到登录页
      localStorage.removeItem('token')
      router.push('/login')
    }
  } catch {
    // 用户取消退出操作
  }
}
</script>

<template>
  <div class="setting-container">
    <div class="setting-header">
      <h2>系统设置</h2>
    </div>
    
    <div class="setting-content">
      <div class="setting-card">
        <h3>账户管理</h3>
        <div class="setting-item">
          <el-button type="danger" @click="handleLogout">退出登录</el-button>
        </div>
      </div>
      
      <!-- 可以添加其他设置项 -->
      <div class="setting-card">
        <h3>系统信息</h3>
        <div class="setting-item">
          <p>系统版本: v1.0.0</p>
          <p>AI风控智能评估平台</p>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.setting-container {
  padding: 20px;
  height: 100%;
}

.setting-header {
  margin-bottom: 20px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.setting-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.setting-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.setting-card h3 {
  margin-bottom: 15px;
  font-size: 18px;
  color: #333;
}

.setting-item {
  margin-top: 15px;
}
</style>
