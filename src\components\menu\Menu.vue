<script setup lang="ts">
import {ref} from 'vue'
import {useRouter} from "vue-router";
import UserInfoWidget from '@/components/UserInfoWidget/UserInfoWidget.vue'// 引入个人信息组件

const router = useRouter()

const menuArr = ref([
  {
    url:'evaluation.png',
    title:'评估表管理',
    select: true,
    routerPath: 'riskEvaluate'
  },

  {
    url:'userlist.png',
    title:'用户列表',
    select: false,
    routerPath: 'user'
  },

  {
    url:'profile.png',
    title:'用户页面',
    select: false,
    routerPath: 'profile'
  },
  {
    url:'profile.png',
    title:'大模型列表',
    select: false,
    routerPath: 'listModel'
  },
])
// const menuHandle = (item) => {
//   for (const ele of menuArr.value) {
//     ele.select = false
//   }
//   item.select = true
//   let path = item.routerPath
//   console.log("path--",path)
//   router.push(path)
// }
const menuHandle = (item) => {
  menuArr.value.forEach(ele => ele.select = false)
  item.select = true
  router.push(item.routerPath)
}

// ✅ 根据 token 判断是否登录
const isLoggedIn = computed(() => !!localStorage.getItem('token'))
</script>

<template>
  <div class="menu">
    <div class="title">AI风控智能评估平台</div>
    <ul>
      <li v-for="(item, index) in menuArr" :key="index" @click="menuHandle(item)">
        <img :src="`/images/menu/${item.url}`" alt="" />
        <span :class="item.select ? 'active' : ''">{{ item.title }}</span>
      </li>
    </ul>
    <!-- 条件渲染用户信息组件 -->
    <div class="user-widget-wrapper" v-if="isLoggedIn">
      <UserInfoWidget />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.menu {
  position: relative;
  .title{
    margin-top: 26px;
    font-size: 24px;
  }
  ul{
    margin-top: 40px;
    margin-left: 85px;
    li{
      width: 192px;
      margin-top: 20px;
      cursor: pointer;
      font-size: 18px;
      display: flex;
      height: 53px;
      line-height: 53px;
      img {
        margin-right: 20px;
        margin-top: 15px;
        width: 24px;
        height: 24px;
      }
      span{
        display: inline-block;
        width: 130px;
        text-align: left;
        padding-left: 20px;
      }
    }
    .active{
      background: #33343F;
      border-radius: 11px;
    }
  }
  text-align: center;
  width: 402px;
  height: 100vh;
  color: #fff;
}
</style>