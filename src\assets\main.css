@import './base.css';

#app {
  padding: 0;
  min-width: 1366px;
  font-weight: normal;
  background-color: black;
  display: flex;
  width: 100%;
  box-sizing: border-box;
  color: black;
}
.clearfix::after {
  content: "";
  display: table;
  clear: both;
}
*{
  padding: 0;
  margin: 0;
  font-family: OPPOSans;
}
ul,li{
  list-style-type: none;
}
a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
  padding: 3px;
}

@media (hover: hover) {
  a:hover {
    background-color: hsla(160, 100%, 37%, 0.2);
  }
}
