<script setup>
import {onMounted, ref} from "vue"
import {exchangeList} from '@/request/riskEvaluateApi'
const maskShow = ref(false)
const pageSize = ref(20)
const total = ref(0)
const currentPage = ref(1)

const detailFrom = ref({
  number: 0,
  otaHotelName: '西咸新区宜悦栖居酒店管理有限公司',
  evaluator: '张三',
  score: 100,
  result: 0,
  state: 0,
})
let detailIndex = 0
// 备用数据,用于取消后恢复数据
const tempDetailFrom = ref({})

const sendMessage = async () => {
  let res = await exchangeList()
  console.log("sendMessage--", res)
}
// 预览按钮
const handleClick = (row,index) => {
  console.log('handleClick', row)
  console.log('handleClick', index)
  detailIndex = index
  maskShow.value = true
  detailFrom.value = row
  tempDetailFrom.value = {
    ...row
  }
}
const cancelMask = () => {
  maskShow.value = false
  console.log("temp---", tempDetailFrom.value)
  // detailFrom.value = {...tempDetailFrom.value}
  tableData[detailIndex] = {
    ...tempDetailFrom.value
  }
  tableData = [...tableData]
  console.log('tableData', tableData)
}

let tableData = [
  {
    number: '01',
    otaHotelName: '西咸新区宜悦栖居酒店管理有限公司',
    evaluator: '张三',
    score: 100,
    result: 1,
    state: 1,
  },
  {
    number: '02',
    otaHotelName: '西咸新区宜悦栖居酒店管理有限公司',
    evaluator: '李四',
    score: 100,
    result: 0,
    state: 1,
  },
  {
    number: '03',
    otaHotelName: '大陆酒店',
    evaluator: '王五',
    score: 100,
    result: 1,
    state: 0,
  },
  {
    number: '04',
    otaHotelName: '大陆酒店',
    evaluator: '张三',
    score: 100,
    result: 0,
    state: 0,
  },
]

onMounted(() => {
  for (let i = 5; i < 25; i++) {
    let param = {
      number: i,
      otaHotelName: '大陆酒店',
      evaluator: '张三',
      score: 100,
      result: 0,
      state: 0,
    }
    tableData.push(param)
  }
  total.value = tableData.length
  console.log("total--", total.value)
})
const handleSizeChange = (val) => {
  console.log(`${val} items per page`)
}
const handleCurrentChange = (val) => {
  console.log(`current page: ${val}`)
}

</script>

<template>
  <div class="risk-evaluate">
    <div class="header">
      <div class="item">
        <span>酒店名称:</span>
        <input type="text" placeholder="请输入酒店名称">
      </div>
      <div class="item">
        <span>评估结果:</span>
        <input type="text" placeholder="请输入评估结果">
      </div>
      <div class="item">
        <span>项目评分:</span>
        <input type="text" placeholder="请输入项目评分">
      </div>
      <div class="item">
        <span>状态:</span>
        <input type="text" placeholder="请输入状态">
      </div>
      <div class="item">
        <div class="search">搜索</div>
      </div>
    </div>
    <div class="table-box">
      <el-table :data="tableData" style="overflow:auto;height:660px; width: 100%; margin-top: 30px;"
                :header-cell-style="{'text-align': 'center'}" :cell-style="{'text-align': 'center'}">
        <el-table-column prop="number" label="编号" width="150"></el-table-column>
        <el-table-column prop="otaHotelName" label="OTA酒店名称" width="260"/>
        <el-table-column prop="evaluator" label="评估员" width="150"/>
        <el-table-column prop="score" label="项目评分" width="150">
          <template #default="scope">{{ scope.row.score }}分</template>
        </el-table-column>
        <el-table-column prop="result" label="评估结果" width="300">
          <template #default="scope">{{ scope.row.result === 1 ? '可以合作' : '不能合作' }}</template>
        </el-table-column>
        <el-table-column prop="state" label="状态">
          <template #default="scope">
            {{ scope.row.state === 1 ? '已提交' : '未提交' }}
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="scope">
            <el-button link type="primary" size="small" @click="handleClick(scope.row,scope.$index)"
            >预览
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="bottom">
      <!--    <div class="line"></div>-->
      <div class="pagination-block">
        <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            layout="prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        />
      </div>
    </div>
    <div class="mask" v-show="maskShow">
      <div class="detail-body">
        <div class="header">
          <div class="close" @click="cancelMask()">
            <img src="@/assets/close.png" alt="">
          </div>
        </div>
        <div class="item-list">
          <el-row class="row">
            <el-col :span="12">
              <div class="item">
                <div class="title">OTA酒店名称:</div>
                <el-input class="input" v-model="detailFrom.otaHotelName" type="text" clearable></el-input>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="item">
                <div class="title">项目评分:</div>
                <el-input class="input" v-model="detailFrom.score" clearable></el-input>
              </div>
            </el-col>
          </el-row>
          <el-row class="row">
            <el-col :span="12">
              <div class="item">
                <div class="title">法人代表:</div>
                <el-input class="input" clearable></el-input>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="item">
                <div class="title">运营主体名称:</div>
                <el-input class="input" clearable></el-input>
              </div>
            </el-col>
          </el-row>
          <el-row class="row">
            <el-col :span="12">
              <div class="item">
                <div class="title">酒店主体性质:</div>
                <el-input class="input" clearable></el-input>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="item">
                <div class="title">酒店对接人:</div>
                <el-input class="input" clearable></el-input>
              </div>
            </el-col>
          </el-row>
          <el-row class="row">
            <el-col :span="12">
              <div class="item">
                <div class="title">是否转销:</div>
                <el-input class="input" clearable></el-input>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="item">
                <div class="title">订单签约主体:</div>
                <el-input class="input" clearable></el-input>
              </div>
            </el-col>
          </el-row>
          <el-row class="row">
            <el-col :span="12">
              <div class="item">
                <div class="title">渠道限制:</div>
                <el-input class="input" clearable></el-input>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="item">
                <div class="title">定价方式:</div>
                <el-input class="input" clearable></el-input>
              </div>
            </el-col>
          </el-row>
          <el-row class="row">
            <el-col :span="12">
              <div class="item">
                <div class="title">酒店纳税资质:</div>
                <el-input class="input" clearable></el-input>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="item">
                <div class="title">支付进度:</div>
                <el-input class="input" clearable></el-input>
              </div>
            </el-col>
          </el-row>
          <el-row class="row">
            <el-col :span="12">
              <div class="item">
                <div class="title">多酒店共享:</div>
                <el-input class="input" clearable></el-input>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="item">
                <div class="title">酒店开票类型:</div>
                <el-input class="input" clearable></el-input>
              </div>
            </el-col>
          </el-row>
          <el-row class="row">
            <el-col :span="12">
              <div class="item">
                <div class="title">现金:</div>
                <el-input class="input" clearable></el-input>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="item">
                <div class="title">特行证情况:</div>
                <el-input class="input" clearable></el-input>
              </div>
            </el-col>
          </el-row>
          <el-row class="row">
            <el-col :span="12">
              <div class="item">
                <div class="title">备注:</div>
                <el-input class="input" clearable></el-input>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="item">
                <div class="title">担保函分数:</div>
                <el-input class="input" clearable></el-input>
              </div>
            </el-col>
          </el-row>
          <el-row class="row">
            <el-col :span="12">
              <div class="item">
                <div class="title">酒店营业时间:</div>
                <el-input class="input" clearable></el-input>
              </div>
            </el-col>
          </el-row>
        </div>
        <div class="detail-foot">
          <div class="line"></div>
          <div class="btn-box">
            <div class="btn edit" @click="maskShow = false">编辑</div>
            <div class="btn submit" @click="maskShow = false">提交</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.risk-evaluate {
  width: 100%;
  padding: 30px;
  position: relative;
  box-sizing: border-box;
  .header {
    display: flex;
    box-sizing: border-box;
    .item {
      height: 32px;
      margin-right: 15px;
      // margin-top: 10px;
      span {
        height: 100%;
        line-height: 32px;
        margin-right: 5px;
      }
      input {
        height: 100%;
        border: 1px solid #4889CB;
        border-radius: 8px;
        text-indent: 10px;
        width: 205px;
      }
      .search {
        text-align: center;
        color: #fff;
        width: 84px;
        height: 100%;
        line-height: 32px;
        background: #001B48;
        border-radius: 8px;
        cursor: pointer;
      }
    }
  }
  .table-box {
    margin-top: 20px;
    width: 100%;
    height: calc(100vh - 160px);
    border-radius: 8px;
    overflow: hidden;
    padding-bottom: 130px;
    box-sizing: border-box;
  }
  .bottom {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 95px;
    width: 100%;
    background-color: #fff;
    z-index: 2;
    //background-color: red;
    .line {
      height: 0.5px;
      background-color: #D8D8D8;
      margin-left: 30px;
      margin-right: 30px;
    }

    .pagination-block {
      position: absolute;
      right: 40px;
      top: 30px;
    }
  }
  .mask {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 99;
    //background-color: red;
    .header {
      height: 28px;
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      background-color: #001B48;
      border-radius: 10px 10px 0px 0px;
      img{
        position: absolute;
        right: 5px;
        top: 5px;
        width: 25px;
        height: 25px;
        cursor: pointer;
      }
    }
    .detail-body {
      border-radius: 8px;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      width: 900px;
      height: 639px;
      background-color: #fff;
      padding: 30px;
      .item-list {
        //display: flex;
        .row{
          margin-bottom: 20px;
        }
        .item {
          display: flex;
          margin-left: 60px;
          .title {
            width: 100px;
          }
          .input {
            width: 200px;
          }
        }
      }
      .detail-foot{
        position: absolute;
        left: 0;
        bottom: 0;
        height: 80px;
        width: 100%;
        .line{
          height: 0.5px;
          margin-left: 50px;
          margin-right: 50px;
          background-color: #979797;
        }
        .btn-box{
          position: absolute;
          left: 50%;
          top: 30px;
          display: flex;
          transform: translateX(-50%);
          .btn{
            width: 84px;
            line-height: 32px;
            background: #001B48;
            border-radius: 8px;
            color: #fff;
            text-align: center;
            font-size: 14px;
            cursor: pointer;
          }
          .edit{
            margin-right: 40px;
          }
        }
      }
    }
  }
}
</style>
<style scoped>

:deep(.el-table.is-scrolling-none th) {
  background: rgba(72, 137, 203, 0.17);
}

:deep(.el-table__header) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table__header tr th) {
  background: rgba(72, 137, 203, 0.17);
  box-shadow: inset 0px -1px 0px 0px #D3D7E1;
  color: #333;
}

:deep(.el-table) {
  /* 透明度为0，不显示背景色 */
  background-color: transparent;
}

/* 表格行背景色 */
:deep(.el-table tr) {
  /* 透明度为0，不显示背景色 */
  background-color: rgba(255, 0, 0, 0);
  /* 文字颜色 */
  color: #222222;
}

/* 鼠标悬停背景色 */
:deep(.el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell) {
  background-color: rgba(116, 85, 228, 0);
}
</style>
