<script setup>
import Menu from '@/components/menu/Menu.vue'
import { useRoute } from 'vue-router'
import { computed } from 'vue'

const route = useRoute()
const isLoginPage = computed(() => {
  console.log('当前路由名称:', route.name)
  return route.name === 'login'
})
</script>

<template>
  <div v-if="isLoginPage">
    <router-view />
  </div>
  <div v-else class="app-container">
    <Menu class="menu"></Menu>
    <div class="main">
      <div class="main-box">
        <router-view />
      </div>
    </div>
  </div>
</template>

<style scoped>
.app-container {
  display: flex;
  width: 100%;
}

.main {
  display: flex;
  width: 100%;
  height: 100vh;
  background-color: black;
  padding: 20px;
  border-radius: 10px;
  overflow: hidden;
  font-size: 14px;
}

.main .main-box {
  width: 100%;
  height: 100%;
  background-color: #fff;
  border-radius: 10px;
  box-sizing: border-box;
  overflow: hidden;
}

@media (min-width: 1024px) {
  header {
    display: flex;
    place-items: center;
    padding-right: calc(var(--section-gap) / 2);
  }
}
</style>