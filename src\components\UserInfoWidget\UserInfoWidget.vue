<script setup>
import { ref, onMounted } from 'vue'
import { getUserInfo, logout } from '@/request/loginApi'
import { ElMessageBox, ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'

const user = ref({})
const router = useRouter()

onMounted(async () => {
  try {
    const res = await getUserInfo()
    user.value = res.user || res.data || res
  } catch (e) {
    ElMessage.error('获取用户信息失败')
  }
})

const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    await logout()
    localStorage.removeItem('token')
    ElMessage.success('退出登录成功')
    router.push('/login')
  } catch {
    // 用户取消操作
  }
}
</script>

<template>
  <div class="user-widget">
    <el-card shadow="hover">
      <el-descriptions column="1">
        <el-descriptions-item label="用户">{{ user.userName || '—' }}</el-descriptions-item>
        <el-descriptions-item label="角色">
          <el-tag type="success" v-if="user.roles?.length">{{ user.roles[0].roleName }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="登录时间">
          {{ user.loginDate ? user.loginDate.slice(0, 19).replace('T', ' ') : '—' }}
        </el-descriptions-item>
        <el-descriptions-item label="">
          <el-button type="danger" size="small" @click="handleLogout">退出</el-button>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
  </div>
</template>

<style scoped>
.user-widget {
  position: absolute;
  bottom: 20px;
  left: 20px;
  width: 300px;
}
</style>
