<script setup>
import { ref, onMounted } from "vue";
import { listUser, addUser, updateUser, delUser } from "@/request/user";
import { ElMessage, ElMessageBox } from "element-plus";

const userList = ref([]);
const total = ref(0);
const pageSize = ref(10); //设置了十条个人信息为一页
const currentPage = ref(1);
const loading = ref(false);
const formRef = ref();

const showDialog = ref(false);
const isEdit = ref(false);
const form = ref({
  userId: null,
  userName: "",
  nickName: "",
  phonenumber: "",
  status: "0",
});

const query = ref({
  userName: "",
  phonenumber: "",
  status: "",
});

const newuserRules = computed(() => {
  const base = {
    userName: [{ required: true, message: "请输入用户名", trigger: "blur" }],
    nickName: [{ required: true, message: "请输入昵称", trigger: "blur" }],
    phonenumber: [
      { required: true, message: "请输入手机号", trigger: "blur" },
      {
        pattern: /^1[3-9]\d{9}$/,
        message: "手机号格式不正确",
        trigger: "blur",
      },
    ],
  };

  if (!isEdit.value) {
    base.password = [
      { required: true, message: "请输入密码", trigger: "blur" },
      {
        min: 7,
        max: 9,
        message: "密码长度必须在7到9位之间",
        trigger: "blur",
      },
    ];
  }

  return base;
});

const getUserList = async () => {
  loading.value = true;
  const res = await listUser({
    pageNum: currentPage.value,
    pageSize: pageSize.value,
    ...query.value,
  });
  userList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

const handleAdd = () => {
  isEdit.value = false;
  form.value = {
    userId: null,
    userName: "",
    nickName: "",
    phonenumber: "",
    password: "",
    status: "0",
  };
  showDialog.value = true;
};

const handleEdit = (row) => {
  isEdit.value = true;
  form.value = {
    userId: row.userId,
    userName: row.userName,
    nickName: row.nickName,
    phonenumber: row.phonenumber,
    password: "",
    status: row.status,
  };
  showDialog.value = true;
};

const handleDelete = async (id) => {
  await ElMessageBox.confirm("确定删除该用户？", "提示", { type: "warning" });
  await delUser(id);
  ElMessage.success("删除成功");
  getUserList();
};

const submitForm = () => {
  formRef.value.validate(async (valid) => {
    if (!valid) return;
    const handler = isEdit.value ? updateUser : addUser;
    try {
      await handler(form.value);
      ElMessage.success(isEdit.value ? "修改成功" : "添加成功");
      showDialog.value = false;
      getUserList();
    } catch (error) {
      // 捕获后端业务异常
      const msg = error?.msg || error?.message || "操作失败";
      ElMessage.error(msg);
    }
  });
};

// 计算用户编号
const formatIndex = (index) => {
  const number = (currentPage.value - 1) * pageSize.value + index + 1;
  return number < 10 ? `0${number}` : `${number}`;
};

// 处理页码变化
const handlePageChange = (page) => {
  currentPage.value = page;
  getUserList();
};

// 处理每页显示数量变化
const handleSizeChange = (size) => {
  pageSize.value = size;
  currentPage.value = 1; // 重置为第一页
  getUserList();
};

onMounted(getUserList);
</script>

<template>
  <div class="user-container">
    <!-- 搜索区域 -->
    <div class="header">
      <!-- 搜索条件输入框 -->
      <div class="item">
        <span>用户名称:</span
        ><el-input v-model="query.userName" placeholder="请输入用户名称" />
      </div>
      <div class="item">
        <span>手机号:</span
        ><el-input v-model="query.phonenumber" placeholder="请输入手机号" />
      </div>
      <div class="item">
        <span>状态:</span>
        <el-select v-model="query.status" placeholder="请选择状态" clearable>
          <el-option label="正常" value="0" />
          <el-option label="停用" value="1" />
        </el-select>
      </div>
      <!-- 操作按钮 -->
      <div class="item">
        <el-button type="primary" @click="getUserList">搜索</el-button>
      </div>
      <div class="item">
        <el-button type="success" @click="handleAdd">新增用户</el-button>
      </div>
    </div>

    <!-- 用户数据表格 -->
    <el-table
      :data="userList"
      v-loading="loading"
      style="width: 100%; margin-top: 20px; box-sizing: border-box"
      :row-class-name="tableRowClassName"
      @row-click="handleRowClick"
    >
      <el-table-column label="用户编号"  :min-width="40" align="center">
        <template #default="scope"> {{ formatIndex(scope.$index) }}</template>
      </el-table-column>
      <el-table-column prop="userName" label="用户名称" :min-width="60" ></el-table-column>
      <el-table-column prop="nickName" label="用户昵称" :min-width="60" ></el-table-column>
      <el-table-column prop="phonenumber" label="手机号码" :min-width="60" ></el-table-column>
      <el-table-column prop="status" label="状态" :min-width="30" >
        <template #default="{ row }">
          <el-tag :type="row.status === '0' ? 'success' : 'danger'">
            {{ row.status === "0" ? "正常" : "停用" }}
          </el-tag>
        </template>
      </el-table-column>

      <!-- 创建时间列 -->
      <el-table-column prop="createTime" label="创建时间" :min-width="60" ></el-table-column>

      <!-- 操作列 -->
      <el-table-column label="操作" fixed="right" :min-width="60" >
        <template #default="{ row }">
          <el-button link size="small" @click="handleEdit(row)">编辑</el-button>
          <el-button
            link
            size="small"
            type="danger"
            @click="handleDelete(row.userId)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
   
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :total="total"
      layout="total, prev, pager, next, jumper"
      style="margin-top: 20px; text-align: right"
      @current-change="handlePageChange"
      @size-change="handleSizeChange"
    />

    <el-dialog
      v-model="showDialog"
      :title="isEdit ? '编辑用户' : '新增用户'"
      width="600px"
    >
      <el-form
        :model="form"
        :rules="newuserRules"
        ref="formRef"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="userName">
          <el-input v-model="form.userName" :disabled="isEdit" />
        </el-form-item>
        <el-form-item label="昵称" prop="nickName">
          <el-input v-model="form.nickName" />
        </el-form-item>
        <el-form-item label="手机号" prop="phonenumber">
          <el-input v-model="form.phonenumber" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="form.password" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showDialog = false">取消</el-button>
        <el-button type="primary" @click="submitForm">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.user-container {
  width: 100%;
  padding: 30px;
  position: relative;
  box-sizing: border-box;
  background-color: #fff;
}

.header {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 20px;
}

.item {
  display: flex;
  align-items: center;
  height: 32px;
  margin-right: 15px;
  margin-bottom: 10px;
}

.item span {
  line-height: 32px;
  margin-right: 5px;
  color: #333;
}

.item input,
.item .el-input,
.item .el-select {
  width: 205px;
  height: 32px;
}

.item .el-input__inner,
.item .el-select__inner {
  border: 1px solid #4889cb;
  border-radius: 16px;
  height: 32px;
  padding-left: 10px;
}

.item .el-button {
  width: 84px;
  height: 32px;
  background: #001b48;
  border-radius: 16px;
  color: white;
}

.el-table {
  width: 100%;
  margin-top: 20px;
}

.el-pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.detail-form {
  padding: 20px;
}

/* 新增表格样式 */
:deep(.el-table.is-scrolling-none th) {
  background: rgba(72, 137, 203, 0.17);
}

:deep(.el-table__header) {
  border-radius: 8px;
  overflow: hidden;
}

/* 表头样式 */
:deep(.el-table__header tr th) {
  background: rgba(72, 137, 203, 0.17);
  box-shadow: inset 0px -1px 0px 0px #d3d7e1;
  color: #333;
  font-weight: 600;
}

:deep(.el-table) {
  background-color: transparent;
}

:deep(.el-table tr) {
  background-color: rgba(255, 0, 0, 0);
  color: #222222;
}

:deep(
    .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell
  ) {
  background-color: rgba(116, 85, 228, 0);
}
:deep(.index-cell) {
  white-space: nowrap;
  display: inline-block;
  width: 100%;
}
:deep(.el-table th),
:deep(.el-table td) {
  padding: 6px 8px;
}

:deep(.el-table__body tr:hover) {
  background-color: #f6faff !important;
}
/* :deep(.el-table__cell) {
  font-size: 15px;
} */

</style>
