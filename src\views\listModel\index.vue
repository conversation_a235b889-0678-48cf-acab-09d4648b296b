<script setup>
import { ref, onMounted } from "vue";
import { listModel } from "@/request/listModelApi";
import { ElMessage } from "element-plus";

const modelList = ref([]); // 模型列表
const total = ref(0); // 总条数
const pageSize = ref(5); // 每页条数
const currentPage = ref(1); // 当前页
const loading = ref(false); // 表格 loading 状态

// 获取模型信息列表
const getModelList = async () => {
  loading.value = true;
  try {
    const res = await listModel({
      current: currentPage.value,
      size: pageSize.value,
    });
    modelList.value = res.data.records;
    total.value = res.data.total;
  } catch (e) {
    ElMessage.error("获取模型数据失败");
  } finally {
    loading.value = false;
  }
};

// 计算分页编号序号
const formatIndex = (index) => {
  const number = (currentPage.value - 1) * pageSize.value + index + 1;
  return number < 10 ? `0${number}` : `${number}`;
};

// 分页事件
const handlePageChange = (page) => {
  currentPage.value = page;
  getModelList();
};

const handleSizeChange = (size) => {
  pageSize.value = size;
  currentPage.value = 1;
  getModelList();
};

onMounted(getModelList);
</script>

<template>
  <div class="model-container">
    <h2>大模型机器人列表</h2>

    <el-table
      :data="modelList"
      v-loading="loading"
      
      stripe
      style="margin-top: 20px"
    >
      <el-table-column label="编号" width="80" align="center">
        <template #default="scope">
          {{ formatIndex(scope.$index) }}
        </template>
      </el-table-column>
      <el-table-column prop="name" label="模型名称" min-width="100" />
      <el-table-column prop="botId" label="机器人ID" min-width="180" />
      <el-table-column
        prop="token"
        label="Token"
        min-width="280"
        show-overflow-tooltip
      />
      <el-table-column prop="createTime" label="创建时间" min-width="160" />
      <el-table-column prop="updateTime" label="更新时间" min-width="160" />
    </el-table>

    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :total="total"
      layout="total, prev, pager, next, jumper"
      style="margin-top: 20px; text-align: right"
      @current-change="handlePageChange"
      @size-change="handleSizeChange"
    />
  </div>
</template>

<style scoped>
.model-container {
  width: 100%;
  padding: 30px;
  position: relative;
  box-sizing: border-box;
  background-color: #fff;
}

/* 表格样式一致化 */
:deep(.el-table.is-scrolling-none th) {
  background: rgba(72, 137, 203, 0.17);
}
:deep(.el-table__header) {
  border-radius: 8px;
  overflow: hidden;
}
:deep(.el-table__header tr th) {
  background: rgba(72, 137, 203, 0.17);
  box-shadow: inset 0px -1px 0px 0px #d3d7e1;
  color: #333;
  font-weight: 600;
}
:deep(.el-table) {
  background-color: transparent;
}
:deep(.el-table tr) {
  background-color: rgba(255, 0, 0, 0);
  color: #222222;
}
:deep(.el-table th),
:deep(.el-table td) {
  padding: 6px 8px;
}
:deep(.el-table__cell) {
  font-size: 15px;
}
:deep(.el-table__body tr:hover > td.el-table__cell) {
  background-color: #f6faff !important;
}
:deep(.index-cell) {
  white-space: nowrap;
  display: inline-block;
  width: 100%;
}

</style>
