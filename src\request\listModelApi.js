import request from '@/request/request'
import { parseStrEmpty } from '@/utils/ruoyi'

// 查询模型列表
export function listModel(query) {
  return request({
    url: '/tyModel/listModel',
    method: 'get',
    params: query
  })
}

// 查询模型详情（ID容错）http://localhost:8080/tyModel/listModel?current=1&size=5

export function getModel(modelId) {
  return request({
    url: '/tyModel/get/' + parseStrEmpty(modelId),
    method: 'get'
  })
}
