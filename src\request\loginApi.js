import http from "./request.js"


// 登录方法
export function login(username, password, code, uuid) {
  const data = {
    username,
    password,
    code,
    uuid
  }
  return http({
    url: '/login',
    headers: {
      isToken: false,
      repeatSubmit: false
    },
    method: 'post',
    data: data
  })
}

// 获取用户信息
export function getUserInfo() {
  return http({
    url: '/getInfo',
    method: 'get'
  })
}

// 退出登录
export function logout() {
  return http({
    url: '/logout',
    method: 'post'
  })
}

// 获取验证码
export function getCodeImg() {
  return http({
    url: '/captchaImage',
    headers: {
      isToken: false
    },
    method: 'get',
    timeout: 20000
  })
}

// 注册方法
export function register(data) {
  return http({
    url: '/register',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}


